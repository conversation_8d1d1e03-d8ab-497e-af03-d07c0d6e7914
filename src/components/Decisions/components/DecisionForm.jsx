import React, { useEffect, useState } from 'react';
import { Row, Col, Button, FormGroup, ControlLabel } from 'react-bootstrap';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import cn from 'classnames';
import to from 'await-to-js';
import SliderConfigItem from './SliderConfigItem';
import SliderPreview from './SliderPreview/SliderPreview';
import IncentivesList from './IncentivesList';
import PNLUpload from './PNLUpload/PNLUpload';
import GroupsSection from './GroupsSection';
import { uploadImage } from '../../../actions/clients';
// Removed MAX_FIELDS import - no longer limiting decision fields
import styles from '../../ClientDetails/client-details.module.scss';

const DecisionForm = ({ state, setState, showAlert }) => {
  const [isImageLoading, setIsImageLoading] = useState(false);

  useEffect(() => {
    if (state) {
      Object.entries(state).forEach(([key, value]) => {
        const field = document.querySelector(`[name="${key}"]`);
        if (field) {
          field.value = value;
        }
      });
    }
  }, [state]);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const orderedSliders = Object.entries(state.sliders)
      .sort(([, a], [, b]) => a.position - b.position)
      .map(([id]) => id);

    const [reorderedItem] = orderedSliders.splice(result.source.index, 1);
    orderedSliders.splice(result.destination.index, 0, reorderedItem);

    const newSliders = { ...state.sliders };
    orderedSliders.forEach((sliderId, index) => {
      newSliders[sliderId] = {
        ...newSliders[sliderId],
        position: index,
      };
    });

    setState((prevState) => ({
      ...prevState,
      sliders: newSliders,
    }));
  };

  const handleSliderConfigChange = (sliderName, configType, value) => {
    setState((prevState) => ({
      ...prevState,
      sliders: {
        ...prevState.sliders,
        [sliderName]: {
          ...prevState.sliders[sliderName],
          [configType]: value,
        },
      },
    }));
  };

  const handleDeleteField = (fieldId) => {
    const currentFields = Object.keys(state.sliders);
    if (currentFields.length <= 1) {
      showAlert('danger', 'Cannot delete the last field');
      return;
    }

    const newSliders = { ...state.sliders };
    delete newSliders[fieldId];

    // Reorder positions
    Object.entries(newSliders)
      .sort(([, a], [, b]) => a.position - b.position)
      .forEach(([id], index) => {
        newSliders[id].position = index;
      });

    setState((prevState) => ({
      ...prevState,
      sliders: newSliders,
    }));
  };

  const handleAddField = () => {
    const currentFields = Object.keys(state.sliders);
    // Removed field limit check - users can now add unlimited fields

    const newFieldNumber = currentFields.length + 1;
    const newFieldId = `field${newFieldNumber}`;

    console.log('Adding new field:', newFieldId, currentFields);
    // return;

    setState((prevState) => ({
      ...prevState,
      sliders: {
        ...prevState.sliders,
        [newFieldId]: {
          fieldName: `Field ${newFieldNumber}`,
          description: '',
          position: currentFields.length,
          labels: [
            { text: 'Low', reflectionA: '', reflectionB: '' },
            { text: 'Medium', reflectionA: '', reflectionB: '' },
            { text: 'High', reflectionA: '', reflectionB: '' },
          ],
        },
      },
    }));
  };

  const handleIncentivesChange = (listId, newIncentives) => {
    setState((prevState) => ({
      ...prevState,
      incentives: {
        ...prevState.incentives,
        [listId]: newIncentives,
      },
    }));
  };

  const handlePNLChange = (pnlData) => {
    setState((prevState) => ({
      ...prevState,
      pnl: pnlData, // Update page-specific PNL data
    }));
  };

  const handleImageUpload = async () => {
    setIsImageLoading(true);

    const file = document.getElementById('page-image').files[0];
    const [err, res] = await to(uploadImage(file));

    setIsImageLoading(false);

    if (err) {
      showAlert('danger', 'Error uploading image');
      return;
    }

    const { url } = res.data;
    showAlert('success', 'Image uploaded successfully');

    setState((prevState) => ({
      ...prevState,
      page_image: url,
    }));
  };

  const handleImageRemove = () => {
    document.getElementById('page-image').value = '';
    setState((prevState) => ({
      ...prevState,
      page_image: '',
    }));
  };

  const renderSliderConfig = (sliderId, index) => (
    <Draggable key={sliderId} draggableId={sliderId} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={{
            padding: '16px',
            marginBottom: '8px',
            background: snapshot.isDragging ? 'rgba(233, 236, 239, 0.8)' : 'rgba(255, 255, 255, 0.3)',
            backdropFilter: 'blur(12px)',
            WebkitBackdropFilter: 'blur(12px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            position: 'relative',
            ...provided.draggableProps.style,
          }}
        >
          <SliderConfigItem
            itemData={{
              id: sliderId,
              displayName: state.sliders[sliderId].fieldName || `Field ${index + 1}`,
              config: state.sliders[sliderId],
            }}
            index={index}
            onConfigChange={handleSliderConfigChange}
            handleDelete={() => handleDeleteField(sliderId)}
          />
        </div>
      )}
    </Draggable>
  );

  const renderPreviews = () => (
    <Row style={{ marginBottom: '30px' }}>
      <Col md={12}>
        <h4>Slider Previews</h4>
        {Object.entries(state.sliders)
          .sort(([, a], [, b]) => a.position - b.position)
          .map(([sliderId, config]) => (
            <SliderPreview key={sliderId} config={config} />
          ))}
      </Col>
    </Row>
  );

  return (
    <div>
      <Row style={{ marginBottom: '20px' }}>
        <Col md={12}>
          <FormGroup>
            <ControlLabel>Page Name</ControlLabel>
            <input
              type="text"
              className="form-control"
              value={state.page_name || ''}
              onChange={(e) => setState(prev => ({ ...prev, page_name: e.target.value }))}
              placeholder="Enter page name"
            />
          </FormGroup>
        </Col>
      </Row>

      <Row style={{ marginBottom: '20px' }}>
        <Col md={12}>
          <FormGroup>
            <ControlLabel>Page Image</ControlLabel>

            {state.page_image ? (
              <div className={styles.preview}>
                <img src={state.page_image} alt="page-image" className={cn('img-responsive', styles.img)} />

                <Button
                  bsStyle="danger"
                  className={styles.button}
                  onClick={handleImageRemove}
                  disabled={isImageLoading}
                >
                  Remove page image
                </Button>
              </div>
            ) : null}

            <input
              id="page-image"
              className="form-control"
              type="file"
              name="page-image"
              accept=".png,.jpg,.jpeg,.svg"
              onChange={handleImageUpload}
              disabled={isImageLoading}
            />
            {isImageLoading && <small className="text-muted">Uploading image...</small>}
          </FormGroup>
        </Col>
      </Row>
      
      <form>
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="sliders">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {Object.entries(state.sliders)
                  .sort(([, a], [, b]) => a.position - b.position)
                  .map(([sliderId], index) => renderSliderConfig(sliderId, index))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>

        <Button
          bsStyle="primary"
          onClick={handleAddField}
          style={{ marginBottom: '30px' }}
        >
          Add Field
        </Button>

        <GroupsSection
          state={state}
          setState={setState}
          showAlert={showAlert}
        />

        {renderPreviews()}

        <Row style={{ marginBottom: '30px' }}>
          <Col md={12}>
            <PNLUpload
              pnlData={state.pnl || { data: null, charts: [] }}
              onChange={handlePNLChange}
              label={'Page P&L Data'}
            />
          </Col>
        </Row>

        <Row>
          <Col md={12}>
            <IncentivesList
              listNumber={1}
              incentives={state.incentives.list}
              optionLimit={state.incentives.optionLimit}
              onChange={(newIncentives) => handleIncentivesChange('list', newIncentives)}
              onOptionLimitChange={(newLimit) =>
                setState((prevState) => ({
                  ...prevState,
                  incentives: {
                    ...prevState.incentives,
                    optionLimit: newLimit,
                  },
                }))
              }
            />
          </Col>
        </Row>
      </form>
    </div>
  );
};

export default DecisionForm;
