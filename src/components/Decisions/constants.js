export const CHARTS = {
  BAR: 'BAR',
  WATERFALL: 'WATERFALL',
  PIE: 'PIE',
};

export const INITIAL_PAGE_STATE = {
  sliders: {
    field1: {
      fieldName: 'Sales Coverage',
      position: 0,
      description: '',
      labels: [
        {
          text: 'Low',
          fte: '6',
          investment: '10000',
        },
        {
          text: 'Medium',
          fte: '8',
          investment: '20000',
        },
        {
          text: 'High',
          fte: '12',
          investment: '30000',
        },
      ],
    },
  },
  incentives: {
    list: [],
    optionLimit: 1,
  },
  page_name: '',
  page_image: '',
  pnl: { data: null, charts: [] },
};
export const MAX_PAGES = 12;
// Removed MAX_FIELDS limit - users can now add unlimited decision fields
